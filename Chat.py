import os
import webbrowser
from flask import Flask, render_template, request
import openai
import pyodbc

app = Flask(__name__)

# OpenAI API client (new SDK)
client = openai.OpenAI(api_key="********************************************************************************************************************************************************************")  # Or use os.getenv("OPENAI_API_KEY")

# SQL Server connection details
connection_string = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=*************;"
    "DATABASE=LNB_New;"
    "UID=db;"
    "PWD=db$098"
)

def generate_sql(table_name, column_name, user_prompt):
    prompt = f"""
Write a SQL Server SELECT query from table '{table_name}' using column '{column_name}'.
User prompt: {user_prompt}.
Return only the SQL query.
"""
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.2
    )
    return response.choices[0].message.content.strip()

def run_sql_query(query):
    try:
        with pyodbc.connect(connection_string) as conn:
            cursor = conn.cursor()
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            results = [dict(zip(columns, row)) for row in rows]
        return results
    except Exception as e:
        return f"Error: {e}"

@app.route("/", methods=["GET", "POST"])
def index():
    results = None
    sql_query = ""
    if request.method == "POST":
        table = request.form.get("table")
        column = request.form.get("column")
        prompt = request.form.get("prompt")

        sql_query = generate_sql(table, column, prompt)
        if "select" in sql_query.lower():
            results = run_sql_query(sql_query)
        else:
            results = "Invalid SQL generated."

    return render_template("chat.html", sql_query=sql_query, results=results)

if __name__ == "__main__":
    webbrowser.open("http://127.0.0.1:5000/")
    app.run(debug=True, use_reloader=False)