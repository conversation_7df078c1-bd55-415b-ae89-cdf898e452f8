import cx_Oracle
cx_Oracle.init_oracle_client(lib_dir="D:\Jaipal\Softwares\instantclient_23_7")
import pandas as pd

# Oracle database connection details
username = 'SYSTEM_AUDIT12'
password = 'SYSTEM_AUDIT12'
dsn = '*************:1521/xe'  # DSN or TNS entry

# Path to save the Excel file
output_file = r"D:\Jaipal\E Drive\Jaipal\Python_Projects\ExportData_ToExcel\ExcelFiles\Data_15022025.xlsx"

# Establish the connection to the Oracle database
connection = cx_Oracle.connect(username, password, dsn)

# Create a cursor object
cursor = connection.cursor()

# Write your SQL query here
sql_query = "select Name,createddatetime from subsegment"

# Execute the query
cursor.execute(sql_query)

# Fetch the results into a pandas DataFrame
df = pd.DataFrame(cursor.fetchall(), columns=[desc[0] for desc in cursor.description])

# Write the DataFrame to an Excel file
df.to_excel(output_file, index=False, engine='openpyxl')

# Close the cursor and connection
cursor.close()
connection.close()

print(f"Data has been written to {output_file}")