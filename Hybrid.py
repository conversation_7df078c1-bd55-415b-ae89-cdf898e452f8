import os
import tempfile
import pandas as pd
import matplotlib.pyplot as plt
from flask import Flask, request, render_template_string, send_file
import requests
import webbrowser
from werkzeug.utils import secure_filename
from PyPDF2 import PdfReader
import docx

app = Flask(__name__)
ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'pdf', 'docx'}

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
  <title>Document Analyzer with Groq</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
<div class="container py-4">
  <h2 class="mb-4">Document Analyzer (Excel / PDF / DOCX)</h2>
  <form method="POST" enctype="multipart/form-data">
    <div class="mb-3">
      <input type="file" name="file" class="form-control" required>
    </div>
    <div class="mb-3">
      <textarea name="prompt" class="form-control" placeholder="E.g. Supplier-wise total invoice sum" required></textarea>
    </div>
    <button type="submit" class="btn btn-primary">Analyze</button>
  </form>

  {% if summary %}
  <hr>
  <h4>LLM Summary:</h4>
  <pre class="bg-light p-3 border">{{ summary }}</pre>
  {% endif %}

  {% if chart_url %}
  <h4>Generated Chart:</h4>
  <img src="{{ chart_url }}" class="img-fluid border">
  {% endif %}
</div>
</body>
</html>
'''
def clean_numeric_column(series):
    """Cleans numeric columns by removing non-numeric characters and converting to float."""
    return (
        series.astype(str)
        .str.replace(r'[^\d.\-]', '', regex=True)  # Remove currency, letters, commas
        .replace('', '0')                         # Replace empty strings with '0'
        .astype(float)
    )
    
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def extract_text_from_pdf(path):
    try:
        reader = PdfReader(path)
        return "\n".join([page.extract_text() or '' for page in reader.pages])
    except Exception as e:
        return f"❌ Error reading PDF: {e}"

def extract_text_from_docx(path):
    try:
        doc = docx.Document(path)
        return "\n".join([para.text for para in doc.paragraphs])
    except Exception as e:
        return f"❌ Error reading DOCX: {e}"

def generate_chart_from_prompt(df, prompt):
    import matplotlib.pyplot as plt
    import os
    import tempfile
    import pandas as pd

    def clean_numeric_column(series):
        # Remove all non-numeric characters except . and -
        cleaned = (
            series.astype(str)
            .str.replace(r'[^\d.\-]', '', regex=True)  # Remove currency, letters, commas
        )
        return pd.to_numeric(cleaned, errors='coerce')

    try:
        df = df.copy()
        df.columns = [col.strip() for col in df.columns]

        print("📊 Columns in DataFrame:", df.columns.tolist())
        print("🔍 First few rows:\n", df.head())

        # Infer text (category) columns
        text_cols = [col for col in df.columns if df[col].dtype == 'object']
        numeric_like_cols = [col for col in df.columns if any(kw in col.lower() for kw in ['amount', 'total', 'sum', 'invoice'])]

        # Try to find good category column
        possible_cat_cols = [col for col in text_cols if any(kw in col.lower() for kw in ['supplier', 'category', 'name'])]
        cat_col = possible_cat_cols[0] if possible_cat_cols else text_cols[0]

        # Find likely numeric column
        num_col = numeric_like_cols[0] if numeric_like_cols else None
        if not num_col:
            # fallback: any column that's not text
            num_candidates = [col for col in df.columns if col not in text_cols]
            num_col = num_candidates[0] if num_candidates else None

        if not cat_col or not num_col:
            print("❌ Could not detect suitable columns.")
            return None

        print(f"🧠 Using category column: '{cat_col}'")
        print(f"💰 Using numeric column: '{num_col}'")

        # Normalize category text
        df[cat_col] = df[cat_col].astype(str).str.strip().str.upper()

        # Clean and convert numeric column
        df[num_col] = clean_numeric_column(df[num_col])

        print("✅ After cleaning:")
        print(df[[cat_col, num_col]].head(10))

        # Drop empty category or amount
        df = df.dropna(subset=[cat_col])
        df = df[df[cat_col] != '']
        df[num_col] = df[num_col].fillna(0)

        # Group and sum
        grouped = df.groupby(cat_col)[num_col].sum().sort_values(ascending=False)
        print("📈 Grouped sum:\n", grouped)

        if grouped.empty:
            return None

        # Plot
        grouped.plot(kind='bar', figsize=(10, 5), title=f'{num_col} per {cat_col}')
        plt.ylabel(f'Sum of {num_col}')
        plt.xlabel(cat_col)
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()

        chart_path = os.path.join(tempfile.gettempdir(), "chart.png")
        plt.savefig(chart_path)
        plt.close()
        return chart_path

    except Exception as e:
        print(f"❌ Chart generation error: {e}")
        return None
    
def get_groupwise_sum_text(df):
    import pandas as pd

    df.columns = [col.strip() for col in df.columns]

    # Normalize column names
    cleaned_cols = {col: col.strip().upper() for col in df.columns}
    df.rename(columns=cleaned_cols, inplace=True)

    # Identify text and numeric-like columns
    text_cols = [col for col in df.columns if df[col].dtype == 'object']

    # Infer category column (e.g., supplier)
    cat_col = next((col for col in text_cols if 'SUPPLIER' in col or 'CATEGORY' in col or 'NAME' in col), text_cols[0])

    # Try all columns to detect numeric ones based on cleaned sum
    potential_num_cols = []
    for col in df.columns:
        try:
            cleaned_col = clean_numeric_column(df[col])
            if cleaned_col.sum() > 0:
                potential_num_cols.append((col, cleaned_col))
        except:
            continue

    if not potential_num_cols:
        return "❌ No numeric columns with sum > 0 found."

    # Prefer invoice/amount/total-type columns
    prioritized = next((col for col, _ in potential_num_cols if any(x in col.lower() for x in ['invoice', 'amount', 'value', 'total'])), potential_num_cols[0][0])
    num_col, cleaned_series = next((col, series) for col, series in potential_num_cols if col == prioritized)

    # Assign cleaned column
    df[num_col] = cleaned_series

    # Normalize category values
    df[cat_col] = df[cat_col].astype(str).str.strip().str.upper()

    # Drop blanks and NaNs
    df = df.dropna(subset=[cat_col])
    df = df[df[cat_col] != '']
    df[num_col] = df[num_col].fillna(0)
    
   
    # Debug prints
    print("🔍 Using category column:", cat_col)
    print("💰 Using numeric column:", num_col)
    print("🧾 First few cleaned rows:\n", df[[cat_col, num_col]].head())
    print("🔢 Total sum:", df[num_col].sum())

    # Group and sum
    grouped = df.groupby(cat_col)[num_col].sum().sort_values(ascending=False)

    return f"📊 Group-wise sum of '{num_col}' per '{cat_col}':\n\n{grouped.to_string()}"
    
def call_groq_llm(prompt):
    api_key = "********************************************************"
    url = "https://api.groq.com/openai/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": "llama3-70b-8192",
        "messages": [
            {"role": "system", "content": "You are a helpful data analyst."},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.5
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()
        return result["choices"][0]["message"]["content"]
    except requests.exceptions.RequestException as e:
        return f"❌ API error: {e}"
    
@app.route("/", methods=["GET", "POST"])
def index():
    summary = chart_url = None

    if request.method == "POST":
        file = request.files.get("file")
        prompt = request.form.get("prompt")

        if file and allowed_file(file.filename):
            ext = file.filename.rsplit('.', 1)[1].lower()
            with tempfile.NamedTemporaryFile(delete=False, suffix='.' + ext) as tmp:
                file.save(tmp.name)
                df = None  # Ensure df is initialized

                try:
                    if ext in ['xlsx', 'xls']:
                        df = pd.read_excel(tmp.name)

                        # Only run this if df is defined (i.e., Excel file)
                        if "group" in prompt.lower() and "sum" in prompt.lower():
                            summary = get_groupwise_sum_text(df)
                        else:
                            df_str = df.to_csv(index=False)
                            full_prompt = f"Analyze this spreadsheet data:\nPrompt: {prompt}\n\n{df_str}"
                            summary = call_groq_llm(full_prompt)

                        # Only generate chart if we have a valid df
                        chart_path = generate_chart_from_prompt(df, prompt)
                        if chart_path:
                            chart_url = f"/chart?path={chart_path}"

                    elif ext == 'pdf':
                        text = extract_text_from_pdf(tmp.name)
                        full_prompt = f"Analyze this PDF content:\nPrompt: {prompt}\n\n{text}"
                        summary = call_groq_llm(full_prompt)

                    elif ext == 'docx':
                        text = extract_text_from_docx(tmp.name)
                        full_prompt = f"Analyze this Word document:\nPrompt: {prompt}\n\n{text}"
                        summary = call_groq_llm(full_prompt)

                except Exception as e:
                    summary = f"❌ File processing error: {e}"

    return render_template_string(HTML_TEMPLATE, summary=summary, chart_url=chart_url)

@app.route("/chart")
def chart():
    path = request.args.get("path")
    if path and os.path.exists(path):
        return send_file(path, mimetype="image/png")
    return "Chart not found", 404

if __name__ == "__main__":
    print("✅ App running at http://127.0.0.1:5000/")
    webbrowser.open("http://127.0.0.1:5000/")
    app.run(debug=True, port=5000, use_reloader=False)