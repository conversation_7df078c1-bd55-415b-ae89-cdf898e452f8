import pandas as pd
import cx_Oracle


excel_file = r"D:\Jaipal\E Drive\Jaipal\Python_Projects\ExportData_ToExcel\ReadFiles\AGRI_SME_AuditTemplate.xlsx"
sheets = pd.ExcelFile(excel_file).sheet_names

for sheet_name in sheets:
    print(f"Inserting data from sheet: {sheet_name}")
    
    # Read data from the current sheet
   ### df = pd.read_excel(excel_file, sheet_name=sheet_name)
    
    # Loop through each row in the dataframe and insert into the database
   ### for index, row in df.iterrows():
        # Construct the PL/SQL insert statement
        # Make sure to adjust the column names and table name accordingly
     ###   insert_query = f"""
    ###    BEGIN
      ###      INSERT INTO your_table_name (column1, column2, column3)
       ###     VALUES (:val1, :val2, :val3);
      ###  END;
      ###  """
        
       # Bind the data from the row to the PL/SQL statement
      ###  cursor.execute(insert_query, val1=row['Column1'], val2=row['Column2'], val3=row['Column3'])
    
    # Commit after inserting data from each sheet
   ### conn.commit()

# Step 4: Close the cursor and connection
###cursor.close()
###conn.close()