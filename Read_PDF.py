import pdfplumber

# Path to your PDF file
pdf_path = r"D:\Jaipal\E Drive\Jaipal\Python_Projects\ExportData_ToExcel\ReadFiles\BankStatemnet_Sample.pdf"
search_term = "ATM"
# Open the PDF
with pdfplumber.open(pdf_path) as pdf:
    for page_number, page in enumerate(pdf.pages, start=1):
        print(f"--- Page {page_number} ---")

        # Extract text using pdfplumber's built-in methods
        text = page.extract_text()
        print(f"Extracted Text:\n{text}")

        # Extract layout elements like tables, which may indicate columns
        table = page.extract_tables()
        if table:
            print("Extracted Table:")
            for row in table:
                print(row)
       
        # Search for specific text
        if search_term.lower() in text.lower():  # Case-insensitive search
            print(f"Found '{search_term}' on Page {page_number}")
            print("Context: ", text[text.lower().find(search_term.lower()): text.lower().find(search_term.lower()) + 200])  # Show a snippet around the found term
        else:
            print(f"'{search_term}' not found on Page {page_number}")
            
        # Extract bounding boxes and text in columns
        for element in page.extract_words():
            print(f"Word: {element['text']} at position: {element['doctop']} with bbox: {element['doctop'], element['top'], element['x0'], element['x1']}")
            
            
       