import cx_Oracle

# Database Connection Configurations
DB_CONFIG = {
    "source_user": "FEDERALV5",
    "source_password": "FEDERALV5",
    "source_dsn": "192.168.0.251:1521/xe",
    "dest_user": "FEDERALV5_BLANK",
    "dest_password": "FEDERALV5_BLANK",
    "dest_dsn": "192.168.0.251:1521/xe"
}

# Output Files
OUTPUT_TXT = "schema_comparison.txt"
OUTPUT_SQL = "schema_sync.sql"

def connect_db(user, password, dsn):
    """Establish Oracle database connection."""
    try:
        return cx_Oracle.connect(user, password, dsn)
    except cx_Oracle.DatabaseError as e:
        print(f"Database connection error: {e}")
        return None

def get_table_ddl(connection, schema, table_name):
    """Fetch full DDL for a table using DBMS_METADATA."""
    cursor = connection.cursor()
    try:
        cursor.execute(f"SELECT DBMS_METADATA.GET_DDL('TABLE', '{table_name}', '{schema}') FROM dual")
        return cursor.fetchone()[0].read()
    except cx_Oracle.DatabaseError as e:
        print(f"Error retrieving DDL for table {table_name}: {e}")
        return None

def get_table_columns(connection, schema):
    """Fetch table column structures (for ALTER TABLE comparison)."""
    cursor = connection.cursor()
    query = f"""
    SELECT table_name, column_name, data_type, data_length 
    FROM all_tab_columns 
    WHERE owner = UPPER('{schema}')
    ORDER BY table_name, column_id
    """
    cursor.execute(query)

    tables = {}
    for row in cursor.fetchall():
        table_name, column_name, data_type, data_length = row
        column_def = f"{column_name} {data_type}({data_length})"
        tables.setdefault(table_name, []).append(column_def)
    return tables

def get_missing_tables(src_conn, dest_conn, src_schema, dest_schema):
    """Find missing tables in the destination schema."""
    src_cursor = src_conn.cursor()
    dest_cursor = dest_conn.cursor()

    src_cursor.execute(f"SELECT table_name FROM all_tables WHERE owner = UPPER('{src_schema}')")
    dest_cursor.execute(f"SELECT table_name FROM all_tables WHERE owner = UPPER('{dest_schema}')")

    src_tables = {row[0] for row in src_cursor.fetchall()}
    dest_tables = {row[0] for row in dest_cursor.fetchall()}

    return src_tables - dest_tables

def get_changed_tables(src_columns, dest_columns):
    """Find tables with altered columns."""
    changed_tables = {}
    
    for table in src_columns:
        if table in dest_columns and src_columns[table] != dest_columns[table]:
            changed_columns = list(set(src_columns[table]) - set(dest_columns[table]))
            changed_tables[table] = changed_columns

    return changed_tables

def get_missing_or_changed_procs(src_conn, dest_conn, src_schema, dest_schema):
    """Find missing or changed stored procedures and functions."""
    src_cursor = src_conn.cursor()
    dest_cursor = dest_conn.cursor()

    src_cursor.execute(f"""
        SELECT object_name, DBMS_METADATA.GET_DDL(object_type, object_name, '{src_schema}') 
        FROM all_objects 
        WHERE owner = UPPER('{src_schema}') AND object_type IN ('PROCEDURE', 'FUNCTION')
    """)

    dest_cursor.execute(f"""
        SELECT object_name, DBMS_METADATA.GET_DDL(object_type, object_name, '{dest_schema}') 
        FROM all_objects 
        WHERE owner = UPPER('{dest_schema}') AND object_type IN ('PROCEDURE', 'FUNCTION')
    """)

    src_objects = {row[0]: row[1].read() for row in src_cursor.fetchall()}
    dest_objects = {row[0]: row[1].read() for row in dest_cursor.fetchall()}

    missing_procs = set(src_objects.keys()) - set(dest_objects.keys())
    changed_procs = {proc for proc in src_objects if proc in dest_objects and src_objects[proc] != dest_objects[proc]}

    return missing_procs, changed_procs, src_objects

def write_output(missing_tables, changed_tables, missing_procs, changed_procs, table_ddls, alter_table_scripts, proc_ddls):
    """Write comparison results and SQL sync scripts to files."""
    with open(OUTPUT_TXT, "w") as txt_file:
        txt_file.write("Missing Tables:\n" + "\n".join(missing_tables) + "\n\n")
        txt_file.write("Changed Tables:\n" + "\n".join(changed_tables.keys()) + "\n\n")
        txt_file.write("Missing Procedures/Functions:\n" + "\n".join(missing_procs) + "\n\n")
        txt_file.write("Changed Procedures/Functions:\n" + "\n".join(changed_procs) + "\n\n")

    with open(OUTPUT_SQL, "w") as sql_file:
        sql_file.write("\n".join(table_ddls) + "\n")
        sql_file.write("\n".join(alter_table_scripts) + "\n")
        sql_file.write("\n".join(proc_ddls) + "\n")

def main():
    src_conn = connect_db(DB_CONFIG["source_user"], DB_CONFIG["source_password"], DB_CONFIG["source_dsn"])
    dest_conn = connect_db(DB_CONFIG["dest_user"], DB_CONFIG["dest_password"], DB_CONFIG["dest_dsn"])

    if not src_conn or not dest_conn:
        print("Error connecting to databases.")
        return

    print("Fetching schema details...")
    missing_tables = get_missing_tables(src_conn, dest_conn, DB_CONFIG["source_user"], DB_CONFIG["dest_user"])
    src_columns = get_table_columns(src_conn, DB_CONFIG["source_user"])
    dest_columns = get_table_columns(dest_conn, DB_CONFIG["dest_user"])
    changed_tables = get_changed_tables(src_columns, dest_columns)
    missing_procs, changed_procs, src_objects = get_missing_or_changed_procs(src_conn, dest_conn, 
                                                                              DB_CONFIG["source_user"], 
                                                                              DB_CONFIG["dest_user"])

    print("Fetching DDL statements for missing tables and procedures...")
    table_ddls = [get_table_ddl(src_conn, DB_CONFIG["source_user"], table) for table in missing_tables]
    proc_ddls = [src_objects[proc] for proc in missing_procs | changed_procs]

    # Generate ALTER TABLE statements
    alter_table_scripts = []
    for table, changed_columns in changed_tables.items():
        for column_def in changed_columns:
            col_name = column_def.split()[0]
            alter_table_scripts.append(f"ALTER TABLE {table} ADD {column_def}; -- Check if column exists before running")

    print("Writing output files...")
    write_output(missing_tables, changed_tables, missing_procs, changed_procs, table_ddls, alter_table_scripts, proc_ddls)

    print(f"Schema comparison saved to {OUTPUT_TXT}")
    print(f"SQL statements saved to {OUTPUT_SQL}")

    src_conn.close()
    dest_conn.close()

if __name__ == "__main__":
    main()