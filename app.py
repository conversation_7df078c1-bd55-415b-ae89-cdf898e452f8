import os
import webbrowser
from flask import Flask, request, jsonify, render_template
import cx_Oracle
import re
import hashlib

app = Flask(__name__)

# --- Utility Functions ---

def connect_db(user, password, dsn):
    try:
        return cx_Oracle.connect(user, password, dsn)
    except cx_Oracle.DatabaseError as e:
        raise ValueError(f"Database connection error: {e}")

def clob_to_str(clob):
    return clob.read() if hasattr(clob, 'read') else str(clob)

def normalize_ddl(ddl):
    """Clean and normalize DDL for accurate comparison."""
    if not ddl:
        return ""

    ddl = clob_to_str(ddl)

    ddl = re.sub(r'/\*.*?\*/', '', ddl, flags=re.DOTALL)  # remove multi-line comments
    ddl = re.sub(r'--.*$', '', ddl, flags=re.MULTILINE)   # remove single-line comments
    ddl = re.sub(r'GRANT .*?;', '', ddl, flags=re.IGNORECASE | re.DOTALL)
    ddl = re.sub(r'"[A-Z0-9_]+"."', '', ddl)              # remove schema qualifiers

    # Remove Oracle-specific metadata
    ddl = re.sub(r'\s+TABLESPACE\s+\S+', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+PCTFREE\s+\d+', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+PCTUSED\s+\d+', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+INITRANS\s+\d+', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+MAXTRANS\s+\d+', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+STORAGE\s*\(.*?\)', '', ddl, flags=re.IGNORECASE | re.DOTALL)
    ddl = re.sub(r'\s+LOGGING', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+NOPARALLEL', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+PARALLEL\s+\d+', '', ddl, flags=re.IGNORECASE)
    ddl = re.sub(r'\s+NOCACHE', '', ddl, flags=re.IGNORECASE)

    ddl = re.sub(r';\s*$', '', ddl.strip())
    ddl = re.sub(r'\s+', ' ', ddl)  # normalize whitespace
    return ddl.upper()

def get_normalized_hash(ddl):
    return hashlib.md5(normalize_ddl(ddl).encode('utf-8')).hexdigest()

# --- Table Comparison ---

def get_missing_or_changed_tables(src_conn, dest_conn, src_schema, dest_schema):
    src_cursor = src_conn.cursor()
    dest_cursor = dest_conn.cursor()

    src_cursor.execute(f"SELECT table_name FROM all_tables WHERE owner = UPPER('{src_schema}')")
    dest_cursor.execute(f"SELECT table_name FROM all_tables WHERE owner = UPPER('{dest_schema}')")

    src_tables = {row[0] for row in src_cursor.fetchall()}
    dest_tables = {row[0] for row in dest_cursor.fetchall()}

    missing_tables = src_tables - dest_tables
    changed_tables = {}

    # Column structure retrieval
    src_cursor.execute(f"""
        SELECT table_name, column_name, data_type, data_length, data_precision, data_scale
        FROM all_tab_columns
        WHERE owner = UPPER('{src_schema}')
    """)
    dest_cursor.execute(f"""
        SELECT table_name, column_name, data_type, data_length, data_precision, data_scale
        FROM all_tab_columns
        WHERE owner = UPPER('{dest_schema}')
    """)

    src_columns = {}
    dest_columns = {}

    for row in src_cursor.fetchall():
        table, column, dtype, length, prec, scale = row
        col_def = build_col_def(dtype, length, prec, scale)
        src_columns.setdefault(table, {})[column] = col_def

    for row in dest_cursor.fetchall():
        table, column, dtype, length, prec, scale = row
        col_def = build_col_def(dtype, length, prec, scale)
        dest_columns.setdefault(table, {})[column] = col_def

    # Compare tables that exist in both
    for table in src_columns:
        if table in dest_columns:
            diffs = []

            src_cols = src_columns[table]
            dest_cols = dest_columns[table]

            # Missing in destination
            for col, src_def in src_cols.items():
                if col not in dest_cols:
                    diffs.append({'action': 'add', 'column': col, 'definition': src_def})
                elif src_def != dest_cols[col]:
                    diffs.append({'action': 'modify', 'column': col, 'definition': src_def, 'existing': dest_cols[col]})

            # Removed in source
            for col, dest_def in dest_cols.items():
                if col not in src_cols:
                    diffs.append({'action': 'remove', 'column': col, 'definition': dest_def})

            if diffs:
                changed_tables[table] = diffs

    return missing_tables, changed_tables, src_columns

# --- Procedure/Function Comparison ---

def get_missing_or_changed_procs(src_conn, dest_conn, src_schema, dest_schema):
    src_cursor = src_conn.cursor()
    dest_cursor = dest_conn.cursor()

    src_cursor.execute(f"""
        SELECT object_type, object_name, DBMS_METADATA.GET_DDL(object_type, object_name, '{src_schema}')
        FROM all_objects
        WHERE owner = UPPER('{src_schema}') AND object_type IN ('PROCEDURE', 'FUNCTION')
    """)
    dest_cursor.execute(f"""
        SELECT object_type, object_name, DBMS_METADATA.GET_DDL(object_type, object_name, '{dest_schema}')
        FROM all_objects
        WHERE owner = UPPER('{dest_schema}') AND object_type IN ('PROCEDURE', 'FUNCTION')
    """)

    src_objects = {
        (row[0], row[1]): clob_to_str(row[2])
        for row in src_cursor.fetchall() if row[2]
    }
    dest_objects = {
        (row[0], row[1]): clob_to_str(row[2])
        for row in dest_cursor.fetchall() if row[2]
    }

    missing_procs = set(src_objects) - set(dest_objects)
    changed_procs = set()

    for proc in src_objects:
        if proc in dest_objects:
            if get_normalized_hash(src_objects[proc]) != get_normalized_hash(dest_objects[proc]):
                changed_procs.add(proc)

    return missing_procs, changed_procs, src_objects

# --- SQL Generator ---

def generate_sql(missing_tables, changed_tables, missing_procs, changed_procs, src_objects, dest_schema, src_columns):
    sql = []

    # Create missing tables
    for table in missing_tables:
        cols = ",\n    ".join(f"{col} {defn}" for col, defn in src_columns[table].items())
        sql.append(f"CREATE TABLE {dest_schema}.{table} (\n    {cols}\n);")

    # Alter changed tables
    for table, diffs in changed_tables.items():
        for diff in diffs:
            col = diff['column']
            if diff['action'] == 'add':
                sql.append(f"ALTER TABLE {dest_schema}.{table} ADD ({col} {diff['definition']});")
            elif diff['action'] == 'remove':
                sql.append(f"-- Warning: Column '{col}' exists in destination but was removed from source\n"
                           f"-- Review before running:\nALTER TABLE {dest_schema}.{table} DROP COLUMN {col};")
            elif diff['action'] == 'modify':
                sql.append(
                    f"-- Modified column detected: {col}\n"
                    f"ALTER TABLE {dest_schema}.{table} MODIFY ({col} {diff['definition']});"
                )

    # Create or replace procedures/functions
    for proc in missing_procs | changed_procs:
        ddl = src_objects[proc]
        sql.append(f"CREATE OR REPLACE {ddl.strip()};")

    return "\n\n".join(sql)

def build_col_def(dtype, length, precision, scale):
    dtype = dtype.upper()
    if dtype in ('NUMBER', 'DECIMAL', 'FLOAT') and precision:
        if scale is not None:
            return f"{dtype}({precision},{scale})"
        else:
            return f"{dtype}({precision})"
    elif dtype in ('VARCHAR2', 'CHAR', 'NCHAR', 'NVARCHAR2'):
        return f"{dtype}({length})"
    else:
        return dtype

# --- Flask Routes ---

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/compare', methods=['POST'])
def compare():
    data = request.get_json()

    src_user = data.get('source_user')
    src_pass = data.get('source_password')
    src_dsn = data.get('source_dsn')
    dest_user = data.get('dest_user')
    dest_pass = data.get('dest_password')
    dest_dsn = data.get('dest_dsn')

    try:
        src_conn = connect_db(src_user, src_pass, src_dsn)
        dest_conn = connect_db(dest_user, dest_pass, dest_dsn)
    except ValueError as e:
        return jsonify({"error": str(e)}), 400

    try:
        missing_tables, changed_tables, src_columns = get_missing_or_changed_tables(src_conn, dest_conn, src_user, dest_user)
        missing_procs, changed_procs, src_objects = get_missing_or_changed_procs(src_conn, dest_conn, src_user, dest_user)

        sql_output = generate_sql(missing_tables, changed_tables, missing_procs, changed_procs, src_objects, dest_user, src_columns)

        src_conn.close()
        dest_conn.close()

        return jsonify({
            "missing_tables": list(missing_tables),
            "changed_tables": changed_tables,
            "missing_procs": list(map(str, missing_procs)),
            "changed_procs": list(map(str, changed_procs)),
            "sql": sql_output
        })

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

if __name__ == '__main__':
    webbrowser.open("http://127.0.0.1:5000/")
    #Using host='0.0.0.0', port=5000 allows the Flask app to be accessible from any other PC on the same LAN by using the IP address of the machine running the app
    #Using seperate port no like 5000, 5001 you can run multiple projects in your pc and other PC on the same LAN can use it with different port nos at a time
    app.run(host='0.0.0.0', port=5000,debug=True, use_reloader=False)
    
    