import os
import webbrowser
from flask import Flask, request, render_template
import pandas as pd
from langchain_community.llms import Ollama
from flask import jsonify

app = Flask(__name__)
UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config["UPLOAD_FOLDER"] = UPLOAD_FOLDER

def clean_and_summarize_dataframe(df):
    df.columns = df.columns.str.strip()

    # Clean numeric columns
    for col in df.columns:
        if 'amount' in col.lower() or 'sales' in col.lower():
            df[col] = df[col].astype(str).str.replace(r'[^\d.-]', '', regex=True)
            df[col] = pd.to_numeric(df[col], errors='coerce')

    summary = {
        "columns": df.columns.tolist(),
        "dtypes": df.dtypes.astype(str).to_dict(),
        "num_rows": len(df),
    }

    summary_text = f"""The uploaded file has {summary['num_rows']} rows.
Columns: {', '.join(summary['columns'])}

Data types:
"""
    for col, dtype in summary['dtypes'].items():
        summary_text += f"- {col}: {dtype}\n"

    summary_text += "\nFull Data (first 100 rows shown to keep it readable):\n"

    # Instead of only 5 rows, show up to 100 for practical reasons
    preview_df = df.head(100)
    for index, row in preview_df.iterrows():
        summary_text += f"{row.to_dict()}\n"

    # Total sales or amount if available
    for col in df.columns:
        if 'amount' in col.lower() or 'sales' in col.lower():
            total = df[col].sum()
            summary_text += f"\nTotal for {col}: ₹{total:,.2f}\n"

    return summary_text

@app.route("/", methods=["GET", "POST"])
def index():
    excel_answer = None
    general_answer = None

    if request.method == "POST":
        llm = Ollama(model="llama3")
        submit_type = request.form.get("submit")

        # ========== Excel Question ==========
        if submit_type == "Ask About Excel":
            file = request.files.get("file")
            question = request.form.get("excel_question")

            if file and question:
                filepath = os.path.join(app.config["UPLOAD_FOLDER"], file.filename)
                file.save(filepath)
                # Get sheet names for dropdown
                sheet_names = pd.ExcelFile(filepath).sheet_names
                selected_sheet = request.form.get("sheet_name")

                if selected_sheet in sheet_names:
                  df = pd.read_excel(filepath, sheet_name=selected_sheet)
                else:
                    df = pd.read_excel(filepath, sheet_name=sheet_names[0])  # Fallback
                context = clean_and_summarize_dataframe(df)

                
            prompt = f"""
                <|system|>
                You are a senior data analyst. You will receive a summary of an Excel spreadsheet and a question about it.
                Analyze the data carefully and respond in clear, simple language. Use bullet points or short explanations if needed.
                <|end|>

                <|user|>
                ### Excel Summary ###
                {context}

                ### Question ###
                {question}

                ### Your Answer:
                <|end|>
                """
            excel_answer = llm.invoke(prompt)

        # ========== General Question ==========
        elif submit_type == "Ask General":
            question = request.form.get("general_question")
            if question:
                prompt = f"""
You are a helpful AI assistant. Answer the following question clearly and concisely.

Question: {question}

Answer:
"""
                general_answer = llm.invoke(prompt)

    return render_template("index.html", 
                           excel_answer=excel_answer, 
                           general_answer=general_answer,
                           sheet_names=sheet_names if 'sheet_names' in locals() else [])
    
@app.route("/sheets", methods=["POST"])
def get_sheets():
    file = request.files.get("file")
    if file:
        filepath = os.path.join(app.config["UPLOAD_FOLDER"], file.filename)
        file.save(filepath)
        sheet_names = pd.ExcelFile(filepath).sheet_names
        return jsonify({"sheets": sheet_names, "filename": file.filename})
    return jsonify({"sheets": []})  

if __name__ == "__main__":
    webbrowser.open("http://127.0.0.1:5000/")
    app.run(debug=True, use_reloader=False)