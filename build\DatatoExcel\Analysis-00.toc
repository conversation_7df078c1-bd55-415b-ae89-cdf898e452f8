(['D:\\Jaipal\\E '
  'Drive\\Jaipal\\Python_Projects\\ExportData_ToExcel\\DatatoExcel.py'],
 ['D:\\Jaipal\\E Drive\\Jaipal\\Python_Projects\\ExportData_ToExcel'],
 [],
 [('D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('d:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('d:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.2 (tags/v3.12.2:6abddd9, Feb  6 2024, 21:26:36) [MSC v.1937 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('DatatoExcel',
   'D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\ExportData_ToExcel\\DatatoExcel.py',
   'PYSOURCE')],
 [('_distutils_hack',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\Jaipal\\Python_Install_Location\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\Jaipal\\Python_Install_Location\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy', 'D:\\Jaipal\\Python_Install_Location\\Lib\\copy.py', 'PYMODULE'),
  ('random', 'D:\\Jaipal\\Python_Install_Location\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct', 'D:\\Jaipal\\Python_Install_Location\\Lib\\struct.py', 'PYMODULE'),
  ('threading',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('bisect', 'D:\\Jaipal\\Python_Install_Location\\Lib\\bisect.py', 'PYMODULE'),
  ('_strptime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('calendar',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\argparse.py',
   'PYMODULE'),
  ('shutil', 'D:\\Jaipal\\Python_Install_Location\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip', 'D:\\Jaipal\\Python_Install_Location\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'D:\\Jaipal\\Python_Install_Location\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Jaipal\\Python_Install_Location\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('gettext',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\gettext.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Jaipal\\Python_Install_Location\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Jaipal\\Python_Install_Location\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('socket', 'D:\\Jaipal\\Python_Install_Location\\Lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri', 'D:\\Jaipal\\Python_Install_Location\\Lib\\quopri.py', 'PYMODULE'),
  ('inspect',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\inspect.py',
   'PYMODULE'),
  ('token', 'D:\\Jaipal\\Python_Install_Location\\Lib\\token.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'D:\\Jaipal\\Python_Install_Location\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Jaipal\\Python_Install_Location\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Jaipal\\Python_Install_Location\\Lib\\ast.py', 'PYMODULE'),
  ('contextlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\contextlib.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\textwrap.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pathlib.py',
   'PYMODULE'),
  ('email',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\Jaipal\\Python_Install_Location\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tempfile.py',
   'PYMODULE'),
  ('tokenize',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'D:\\Jaipal\\Python_Install_Location\\Lib\\queue.py', 'PYMODULE'),
  ('platform',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\platform.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'D:\\Jaipal\\Python_Install_Location\\Lib\\signal.py', 'PYMODULE'),
  ('json',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('pkgutil',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipimport.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'D:\\Jaipal\\Python_Install_Location\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'D:\\Jaipal\\Python_Install_Location\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\secrets.py',
   'PYMODULE'),
  ('hmac', 'D:\\Jaipal\\Python_Install_Location\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Jaipal\\Python_Install_Location\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Jaipal\\Python_Install_Location\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\client.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site', 'D:\\Jaipal\\Python_Install_Location\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('shlex', 'D:\\Jaipal\\Python_Install_Location\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Jaipal\\Python_Install_Location\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._elffile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('plistlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\plistlib.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._setuptools_logging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.bdist_wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('glob', 'D:\\Jaipal\\Python_Install_Location\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\__future__.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('pandas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas._typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil._common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('six',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pickletools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pickletools.py',
   'PYMODULE'),
  ('doctest',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\doctest.py',
   'PYMODULE'),
  ('pdb', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'D:\\Jaipal\\Python_Install_Location\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Jaipal\\Python_Install_Location\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\Jaipal\\Python_Install_Location\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Jaipal\\Python_Install_Location\\Lib\\cmd.py', 'PYMODULE'),
  ('pandas.core.arrays.interval',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.util.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.io._util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('numpy.ma',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.records',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.lib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.linalg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy._core.tests._natype',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy.testing._private',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('uuid', 'D:\\Jaipal\\Python_Install_Location\\Lib\\uuid.py', 'PYMODULE'),
  ('pandas.io.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pytz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._config.config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.series',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.io.json',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('numpy.typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE')],
 [('python312.dll',
   'D:\\Jaipal\\Python_Install_Location\\python312.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\json.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\join.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\index.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('cx_Oracle.cp312-win_amd64.pyd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\cx_Oracle.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Jaipal\\Python_Install_Location\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Jaipal\\Python_Install_Location\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\Jaipal\\Python_Install_Location\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY')],
 [],
 [],
 [('pandas\\io\\formats\\templates\\string.tpl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('numpy-2.2.3.dist-info\\METADATA',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy-2.2.3.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.3.dist-info\\DELVEWHEEL',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy-2.2.3.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.3.dist-info\\INSTALLER',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy-2.2.3.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.3.dist-info\\WHEEL',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy-2.2.3.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.3.dist-info\\entry_points.txt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy-2.2.3.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.3.dist-info\\LICENSE.txt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy-2.2.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.3.dist-info\\RECORD',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy-2.2.3.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'D:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\ExportData_ToExcel\\build\\DatatoExcel\\base_library.zip',
   'DATA')])
