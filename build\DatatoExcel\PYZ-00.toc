('D:\\Jaipal\\E '
 'Drive\\Jaipal\\Python_Projects\\ExportData_ToExcel\\build\\DatatoExcel\\PYZ-00.pyz',
 [('__future__',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\Jaipal\\Python_Install_Location\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\Jaipal\\Python_Install_Location\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Jaipal\\Python_Install_Location\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Jaipal\\Python_Install_Location\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Jaipal\\Python_Install_Location\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd', 'D:\\Jaipal\\Python_Install_Location\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Jaipal\\Python_Install_Location\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Jaipal\\Python_Install_Location\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Jaipal\\Python_Install_Location\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Jaipal\\Python_Install_Location\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'D:\\Jaipal\\Python_Install_Location\\Lib\\dis.py', 'PYMODULE'),
  ('doctest',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Jaipal\\Python_Install_Location\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Jaipal\\Python_Install_Location\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob', 'D:\\Jaipal\\Python_Install_Location\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Jaipal\\Python_Install_Location\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac', 'D:\\Jaipal\\Python_Install_Location\\Lib\\hmac.py', 'PYMODULE'),
  ('html',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Jaipal\\Python_Install_Location\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Jaipal\\Python_Install_Location\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Jaipal\\Python_Install_Location\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Jaipal\\Python_Install_Location\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\Jaipal\\Python_Install_Location\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Jaipal\\Python_Install_Location\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Jaipal\\Python_Install_Location\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\Jaipal\\Python_Install_Location\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._setuptools_logging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.bdist_wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Jaipal\\Python_Install_Location\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Jaipal\\Python_Install_Location\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Jaipal\\Python_Install_Location\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Jaipal\\Python_Install_Location\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\Jaipal\\Python_Install_Location\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\Jaipal\\Python_Install_Location\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Jaipal\\Python_Install_Location\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\threading.py',
   'PYMODULE'),
  ('token', 'D:\\Jaipal\\Python_Install_Location\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Jaipal\\Python_Install_Location\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Jaipal\\Python_Install_Location\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'd:\\Jaipal\\E '
   'Drive\\Jaipal\\Python_Projects\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'D:\\Jaipal\\Python_Install_Location\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\Jaipal\\Python_Install_Location\\Lib\\zipimport.py',
   'PYMODULE')])
