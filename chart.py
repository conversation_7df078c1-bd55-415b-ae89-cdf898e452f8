import webbrowser
from flask import Flask, render_template, request, send_file, redirect, url_for
import pandas as pd
import matplotlib.pyplot as plt
import pyodbc
import os
from io import BytesIO
from langchain.llms import Ollama
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

app = Flask(__name__)

# SQL Server Connection Config - replace with your actual values
server = '192.168.0.252'
database = 'Steinweg_Prod'
username = 'db'
password = 'db$098'
driver = 'ODBC Driver 17 for SQL Server'

conn_str = f"""
    DRIVER={{{driver}}};
    SERVER={server};
    DATABASE={database};
    UID={username};
    PWD={password};
    TrustServerCertificate=yes;
"""
conn = pyodbc.connect(conn_str)

# Load Mistral LLM
llm = Ollama(model="mistral")

# SQL + Chart type prompt template
sql_prompt = PromptTemplate.from_template("""
Given the SQL Server database schema and a natural language question,
generate the appropriate SQL query and also suggest a chart type (bar or line)
based on what best visualizes the result.

Schema:
{schema}

Question: {question}

Respond in the following format:

SQL: <SQL query>
Chart: <bar/line>
""")

generated_sql_global = ""  # To store generated SQL for download

def get_db_schema():
    cursor = conn.cursor()
    tables = cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'").fetchall()
    schema = ""
    for table in tables:
        table_name = table[0]
        cols = cursor.execute(f"""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
        """).fetchall()
        col_defs = ", ".join([f"{col[0]} ({col[1]})" for col in cols])
        schema += f"{table_name}: {col_defs}\n"
    return schema

def get_tables():
    cursor = conn.cursor()
    tables = cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'").fetchall()
    return [t[0] for t in tables]

@app.route('/', methods=['GET', 'POST'])
def index():
    global generated_sql_global
    chart_url = None
    generated_sql = None
    detected_chart_type = None

    tables = get_tables()

    if request.method == 'POST':
        user_prompt = request.form['nl_prompt']
        manual_chart_type = request.form.get('chart_type_manual', '').lower()
        selected_table = request.form.get('table', '').strip()

        try:
            # Build schema snippet, optionally focused on selected table
            schema = get_db_schema()
            if selected_table:
                # Filter schema to only selected table for smaller prompt (optional)
                schema_lines = [line for line in schema.splitlines() if line.startswith(selected_table + ":")]
                schema = "\n".join(schema_lines) if schema_lines else schema

            chain = LLMChain(llm=llm, prompt=sql_prompt)
            llm_response = chain.run(question=user_prompt, schema=schema).strip()
            lines = llm_response.splitlines()

            # Parse SQL and chart type from LLM output
            generated_sql = next((line.replace("SQL:", "").strip() for line in lines if line.lower().startswith("sql:")), None)
            detected_chart_type = next((line.replace("Chart:", "").strip().lower() for line in lines if line.lower().startswith("chart:")), "bar")

            # If user manually selected a chart type, override detected one
            chart_type = manual_chart_type if manual_chart_type in ['bar', 'line'] else detected_chart_type

            # Save for download
            generated_sql_global = generated_sql

            # Run SQL and get dataframe
            df = pd.read_sql(generated_sql, conn)

            # Generate chart
            plt.figure(figsize=(10, 6))
            x_col, y_col = df.columns[:2]

            if chart_type == 'bar':
                df.groupby(x_col)[y_col].sum().plot(kind='bar')
            elif chart_type == 'line':
                df.sort_values(x_col).plot(x=x_col, y=y_col, kind='line')
            else:
                # fallback bar chart
                df.groupby(x_col)[y_col].sum().plot(kind='bar')

            plt.title(f"{chart_type.capitalize()} Chart")
            plt.ylabel(y_col)
            plt.tight_layout()

            os.makedirs('static', exist_ok=True)
            chart_path = 'static/chart.png'
            plt.savefig(chart_path)
            plt.close()

            chart_url = chart_path

        except Exception as e:
            return f"<h4>Error:</h4> {e}<br><br><h4>Generated SQL:</h4><pre>{generated_sql or 'None'}</pre>"

    return render_template('chart.html',
                           chart_url=chart_url,
                           generated_sql=generated_sql,
                           detected_chart_type=detected_chart_type,
                           tables=tables)

@app.route('/download_sql')
def download_sql():
    global generated_sql_global
    if not generated_sql_global:
        return redirect(url_for('chart'))
    return (generated_sql_global, 200, {
        'Content-Type': 'text/sql',
        'Content-Disposition': 'attachment; filename=query.sql'
    })

if __name__ == '__main__':
    webbrowser.open("http://127.0.0.1:5000/")
    app.run(debug=True, use_reloader=False)