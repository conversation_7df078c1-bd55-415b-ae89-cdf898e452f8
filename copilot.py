import webbrowser
import os
from flask import Flask, request, render_template
import docx
import PyPDF2
from collections import Counter
from PIL import Image
import pytesseract
import pandas as pd
from pdf2image import convert_from_path
from langdetect import detect
from sumy.parsers.plaintext import PlaintextParser
from sumy.nlp.tokenizers import Tokenizer
from sumy.summarizers.lsa import LsaSummarizer

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'xlsx', 'png', 'jpg', 'jpeg'}

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def extract_text_from_docx(file_path):
    doc = docx.Document(file_path)
    return "\n".join([para.text for para in doc.paragraphs])

def extract_text_from_pdf(file_path):
    with open(file_path, 'rb') as f:
        reader = PyPDF2.PdfReader(f)
        return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])

def extract_text_from_scanned_pdf(file_path):
    images = convert_from_path(file_path)
    text = ""
    for img in images:
        text += pytesseract.image_to_string(img)
    return text

def extract_text_from_image(file_path):
    image = Image.open(file_path)
    return pytesseract.image_to_string(image)

def extract_text_from_excel(file_path):
    df = pd.read_excel(file_path)
    return df.to_string(index=False)

def summarize_text(text, sentence_count=3):
    parser = PlaintextParser.from_string(text, Tokenizer("english"))
    summarizer = LsaSummarizer()
    summary = summarizer(parser.document, sentence_count)
    return " ".join(str(sentence) for sentence in summary)

def analyze_text(text):
    words = text.split()
    word_count = len(words)
    keyword_freq = Counter(words)
    summary = summarize_text(text) if len(words) > 50 else "Text too short for summary"
    language = detect(text) if len(text) > 20 else "Unknown"
    return word_count, keyword_freq.most_common(10), summary, language

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        if 'file' not in request.files:
            return render_template("copilot.html", error="No file uploaded.")

        file = request.files['file']
        if file and allowed_file(file.filename):
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filepath)

            if file.filename.endswith('.docx'):
                text = extract_text_from_docx(filepath)
            elif file.filename.endswith('.pdf'):
                try:
                    text = extract_text_from_pdf(filepath)
                    if not text.strip():
                        text = extract_text_from_scanned_pdf(filepath)
                except Exception:
                    text = extract_text_from_scanned_pdf(filepath)
            elif file.filename.endswith('.xlsx'):
                text = extract_text_from_excel(filepath)
            elif file.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                text = extract_text_from_image(filepath)
            else:
                return render_template("copilot.html", error="Unsupported file type.")

            word_count, top_keywords, summary, language = analyze_text(text)
            return render_template("copilot.html", text=text, word_count=word_count,
                                   top_keywords=top_keywords, summary=summary, language=language)

    return render_template("copilot.html")

if __name__ == '__main__':
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    webbrowser.open("http://127.0.0.1:5000/")
    app.run(debug=True, use_reloader=False)