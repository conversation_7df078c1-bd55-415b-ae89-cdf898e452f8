from flask import Flask, render_template, request
import pandas as pd
import matplotlib.pyplot as plt
import pyodbc
import cx_Oracle
import os
import requests
import webbrowser
import threading
import re


app = Flask(__name__)


# 🔐 Replace with your actual Groq API Key
GROQ_API_KEY = "********************************************************"
GROQ_URL = "https://api.groq.com/openai/v1/chat/completions"


# 📡 Connect to MS SQL Server
def connect_mssql(server, port, db, user, password):
    conn_str = (
        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
        f"SERVER={server},{port};DATABASE={db};UID={user};PWD={password}"
    )
    return pyodbc.connect(conn_str)

# 🧠 Call Groq API to generate SQL
def query_llm(prompt):
    if not prompt or not isinstance(prompt, str):
        raise ValueError("Prompt is missing or invalid.")

    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "llama3-70b-8192",
        "messages": [
            {
                "role": "system",
                "content": (
                    "You are a SQL assistant for Microsoft SQL Server. "
                    "Always cast numeric-like text columns (e.g., NVARCHAR) to DECIMAL before using SUM, AVG, etc. "
                    "Return only the SQL query, no explanation or markdown."
                )
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    }

    try:
        response = requests.post(GROQ_URL, headers=headers, json=data)
        response.raise_for_status()
        json_data = response.json()

        if "choices" not in json_data:
            raise ValueError(f"Unexpected LLM response: {json_data}")

        return json_data["choices"][0]["message"]["content"]

    except Exception as e:
        raise RuntimeError(f"LLM API call failed: {str(e)}")


# 🧹 Clean SQL from Groq response
def extract_sql_code(llm_response):
    # Remove markdown syntax
    code = re.sub(r"```(?:sql)?", "", llm_response, flags=re.IGNORECASE)
    code = code.replace("```", "").strip()

    # Extract SQL starting from SELECT or similar
    match = re.search(r"(SELECT|WITH|INSERT|UPDATE|DELETE|EXEC)\s.+", code, re.IGNORECASE | re.DOTALL)
    return match.group(0).strip() if match else code


def fix_numeric_sum_cast(sql):
    # Use regex to find SUM(column) without affecting aliases
    def replacer(match):
        col = match.group(1).strip()
        return f"SUM(CAST({col} AS DECIMAL(18,2)))"

    # Replace only SUM(...) calls and leave aliases outside
    fixed_sql = re.sub(r"SUM\(\s*([a-zA-Z0-9_\.]+)\s*\)", replacer, sql, flags=re.IGNORECASE)
    return fixed_sql


# 🏃 Run SQL query and return DataFrame
def run_query(conn, query):
    return pd.read_sql(query, conn)


# 📊 Create chart and save to static folder
def plot_chart(df, chart_type='bar'):
    chart_path = None
    if not df.empty and df.shape[1] >= 2:
        x = df.iloc[:, 0].astype(str)
        y_raw = df.iloc[:, 1]

        y = pd.to_numeric(y_raw, errors='coerce').fillna(0)

        plt.figure(figsize=(10, 5))

        if chart_type == 'line':
            plt.plot(x, y, marker='o', color='#59a14f')
        elif chart_type == 'pie':
            plt.pie(y, labels=x, autopct='%1.1f%%', startangle=140)
        else:
            bars = plt.bar(x, y, color='#4e79a7', edgecolor='black')
            for bar in bars:
                height = bar.get_height()
                plt.annotate(f'{height:.2f}', xy=(bar.get_x() + bar.get_width() / 2, height),
                             xytext=(0, 3), textcoords="offset points", ha='center', fontsize=8)

        if chart_type != 'pie':
            plt.xticks(rotation=45, ha='right')
            plt.ylabel("Value", fontsize=12)
            plt.title("Auto-generated Chart", fontsize=14)
            plt.tight_layout()

        chart_path = os.path.join('static', 'chart.png')
        plt.savefig(chart_path)
        plt.close()

    return chart_path


# 🌐 Main route
@app.route('/', methods=['GET', 'POST'])
def index():
    result, chart_path, query, error, prompt = None, None, None, None, None

    if request.method == 'POST':
        prompt = request.form['prompt']
        server = request.form['server']
        port = request.form['port']
        db = request.form['database']
        user = request.form['username']
        password = request.form['password']
        chart_type = request.form.get('chart_type', 'bar').lower()

        try:
            raw_response = query_llm(prompt)
            query = extract_sql_code(raw_response)
            query = fix_numeric_sum_cast(query)

            if not query.strip().lower().startswith("select"):
                raise ValueError("Only SELECT statements are allowed.")

          
            conn = connect_mssql(server, port, db, user, password)
            
            df = run_query(conn, query)
            chart_path = plot_chart(df, chart_type)
            result = df.to_html(classes='table table-bordered', index=False)
            conn.close()

        except Exception as e:
            error = f"Error: {str(e)}"

    return render_template('db_chart.html', result=result, chart_path=chart_path, query=query, error=error)


# 🚀 Auto-open browser
def open_browser():
    webbrowser.open_new("http://127.0.0.1:5000/")


if __name__ == '__main__':
    if not os.path.exists('static'):
        os.mkdir('static')
    threading.Timer(1.0, open_browser).start()
    #Using host='0.0.0.0', port=5000 allows the Flask app to be accessible from any other PC on the same LAN by using the IP address of the machine running the app
    app.run(host='0.0.0.0', port=5000,debug=True, use_reloader=False)