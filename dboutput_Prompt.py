import os
from flask import Flask, request, render_template, jsonify
import cx_Oracle
import pyo**c
import webbrowser
from groq import Groq


app = Flask(__name__)

groq_client = Groq(api_key="********************************************************")

# ----- Connection Strings -----
ORACLE_CONFIG = {
    "user": "SYSTEM_AUDIT13",
    "password": "SYSTEM_AUDIT13",
    "dsn": "192.168.0.250:1521/xe"
}

SQLSERVER_CONN_STR = "DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.0.252;DATABASE=RPG;UID=**;PWD=**$098"

# ----- DB Utilities -----
def get_oracle_tables():
    conn = cx_Oracle.connect(**ORACLE_CONFIG)
    cursor = conn.cursor()
    cursor.execute("SELECT table_name FROM user_tables")
    tables = [row[0] for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return tables

def get_sqlserver_tables():
    conn = pyo**c.connect(SQLSERVER_CONN_STR)
    cursor = conn.cursor()
    cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_type='BASE TABLE'")
    tables = [row[0] for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return tables

def prompt_to_sql(prompt, tables, **_type):
    table_hint = ", ".join(tables)
    full_prompt = (
        f"You are an expert SQL developer using {**_type.upper()} database.\n"
        f"Given these tables: {table_hint}, write an SQL query for the following prompt:\n"
        f"{prompt}\n\n"
        f"Use only valid {**_type.upper()} SQL syntax."
    )

    response = groq_client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=[{"role": "user", "content": full_prompt}],
        temperature=0.2
    )
    return response.choices[0].message.content.strip()

# ----- Routes -----
@app.route("/", methods=["GET", "POST"])
def index():
    query_result = ""
    error = ""
    tables = []

    if request.method == "POST":
        **_type = request.form.get("**_type")
        prompt = request.form.get("prompt")

        try:
            if **_type == "oracle":
                tables = get_oracle_tables()
            elif **_type == "sqlserver":
                tables = get_sqlserver_tables()
            else:
                error = "Invalid DB type selected."

            if not error:
                query_result = prompt_to_sql(prompt, tables, **_type)  # ✅ Fixed here
        except Exception as e:
            error = str(e)

    return render_template("**output_Prompt.html", query_result=query_result, tables=tables, error=error)

if __name__ == "__main__":
    webbrowser.open("http://127.0.0.1:5000/")
    app.run(debug=True, use_reloader=False)