Missing Tables:
DUN<PERSON><PERSON>MST_BKP
DDACCOUNTSTATEMENT_BKP
CALCINTERESTDUE<PERSON>ILYTEMP
DAILYINTONINTHISTORYMST
IOIMST
ECBFSCHEDULEREMAILTEMP
INTERESTISOREF
DAILYINTERESTDUEHISTORYMST
IOIMSTTEMP
DDACCOUNTSTATEMENT_BKP2
INTERESTONINTCALCTEMPDAILY

Changed Tables:
BORROWERMST
DOCUMENTREPOSITORYMST
DOCUMENTREPOSITORYMSTTEMP
ECBFSCHEDULEREMAIL
LBWHRVALUATIONMST
LBWHRVALUATIONREF
LBWHRVALUATIONREFTEMP
LLWHRREF
LLWHRREFTEMP
NEFT_RTGS_API_DETAILS
SETUP
TT_LOANBOOKEDOSREPORT
TT_LOANDETAILS

Missing Procedures/Functions:
CALCINTERESTONINTDAILY
GETNOOFDAYSNEW
GETINTERESTFORINTONINT
CALCINTERESTFORFORECAST
CALCINTERESTDUEDAILYHISTORY
CALCINTERESTFORFORECASTBU<PERSON><PERSON>
CALCULATEMONTHLYINTONINTFOROS
CALCINTERESTFORREPAYINTONINT

Changed Procedures/Functions:
MAKEPAYMENTINQUIRYMARGINCALLEX
CALCULATEPENALINTERESTFOROUTST
CALCINTERESTFORMONTHLYREPAY
PERIODMST_TEMPSEL
CALCULATEINTERESTFORMONTHLYREP
GET_ENC_VAL
TEST
CLOBTOBLOB_SQLDEVELOPER
INSERTLLMONTHLYINTERESTREPAYME
LIST4
FN_SPLIT
CALCULATEINTERESTHUBWISE
GETINTERESTAMOUNTPAID
SLABCALCULATECMPAYPOUTONFIX
BORROWERDISBACTIVITY
FNINITRC4
SLABCALCULATECMPAYPOUTONPERS
SMAINTERESTPENAL
CALCULATECMPAYOUT
INSERTLLMONTHLYINTERESTREPAY
LIST3
GETINTEREST
GETPAYMENT
GETGRANULARITYREPORTFORBULK
CALCULATECMPAYOUTFORBULK
CALCULATECMPAYOUTFORAVGWISE
TRANSACTIONENTRY
LIST
LBLTRANSACTIONENTRYFORPARTIAL
GETINSURANCETOBEPAID
DATEADD
SPLIT_BYDELIMITER
GETPENDINGFORAPPROVALREPORT
MAKEPAYMENTINQUIRYMARGINCALL
CALCULATECMPAYPOUTONPERS
BORROWERDISBURESMENTREPORT1
BORROWERDISBURESMENTREPORT
SETPAYMENT
CALCULATEINTERESTFORSTRUCTFARM
CALCULATEINTERESTHUBWISENEWB
WAREHOUSEMSTRECORDS
UPDATEMONTHLYINTERESTMST
LIST1
SAVESPRETAILM2MPRICEPRNEW
WEEKDAYSININTERVAL
GENERATEINTERESTDUE
CALCULATECOMPOUNDINTEREST
LOANBOOKEDOSREPORTDEMAT
TESTINGWHILELOOP
CALCULATETAT
PERIODMST_TEMP
TRANSACTIONENTRYFORPARTIAL
GETMISDETAILS
TESTGETNOOFDAYS
CALCULATEBULKPRICEWRWISEM2M
CALCULATEINTERESTFORCOMPOUND_2
CALCULATESPRETAILQUALITYM2M
WAREHOUSEMSTRECORDS4OUTSTANDIN
GETQTYRELEASE
JOBTESTPROC
COMMODITYCATEGORYREPORT
CALCULATEINTERESTFOROS2
CALCULATEMONTHLYINTERESTFOROS
ENCRYPT_FIELD
FNENCDECRC4
ECBFSCHEDULERDAILY
LIST2
BORROWERDISBACTIVITY1
GETGRANULARITYREPORTFORRETAIL
BORROWERDISBACTIVITYMAIN
BORROWERFINALISEDREPORT
CALCULATECMPAYPOUTFORFIXPAYOUT
UP_PASSLIST1
GETCOUNTDAYS
MAKEPAYMENTINQUIRY
LIST5
ADD_COLUMN
MARGINCALLWRNOGETDETAILS4WRNO
DISBURSEMENTCHECKINMNCLEAFALLO
LBLTRANSACTIONENTRY
CALCINTERESTCOMPOUNDPENALOS
CALCINTERESTCOMPOUNDFOROS
DATEDIFF
BORROWERDISBACTIVITYTRACKERLIS
SAVESPRETAILM2MPRICEQUALITYPR
STOCKREPORT
CALCULATESPRETAILPRICEM2MNEW
CALCULATEMONTHLYINTERESTDAILY
UP_PASSLIST
CALCULATEINTERESTFOROS
CALCULATEBULKPRICEM2MFORWHR
GETTOTALWAREHOUSEUSEDDAYS
EXPORTBORROWERACCOUNTREPORT
PUREDATE
GETMAXDPD
LOANBOOKEDOSREPORT
CALCULATEBULKQUALITYWRWISEM2M
GETFREESTOCK
BORROWERDISBACTIVITYTRACKERL_2
GETNOOFDAYS
CALCULATEBULKPRICEM2M
CALCULATECMPAYOUTDETAILSFORVIE
GETGRANULARITYREPORTFORSP
GETALPHANUMERIC
CALCULATECMCHARGES
CALCULATEINTERESTHUBWISENEW
REVISEDLOANBOOKEDOSREPORT
SAVESPRETAILM2MPRICEQUALITYPRO
CALCULATEINTERESTFORBORROWER
GETROUNDNUMBER
CALCINTERESTDAILY
PAYMENTDETAIL
CALCULATECOMPOUNDINTEREST_2
INSERTINTERESTREPORT
CALCULATEINTERESTFORCOMPOUND
CALCULATEPENALINTERESTFOROS
BACKDATEDREPAYMENTFORMONTHLYTY
GETUSERS
SMAINTERESTSLAB
CALCINTERESTBULLETPENALOS
ROHANM2M
MAKEPAYMENTINQUIRY1
BORROWERDISBACTIVITYTRACKERL_1
CALCULATEINTEREST
GETBULKM2MPRICEQUALITYPROCESSD
CALCULATESPRETAILPRICEM2M
GETWRPAYMENTDETAIL
DISBURSEDWRSINLISTRECEIPTALLOC
NEWSTOCKREPORT
CALCULATEINTERESTFORSTRUCTFA_2
SAVEBULKM2MPRICEQUALITYPROCESS
MARGINCALLWRNOGETDETAILS
DAILYUTILIZATION

