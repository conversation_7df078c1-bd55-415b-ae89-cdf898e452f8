 body {
      font-family: 'Segoe UI', sans-serif;
      background: #f0f4f8;
      color: #333;
      padding: 2rem;
    }
    .container {
      max-width: 800px;
      margin: auto;
      background: #fff;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      position: relative;
    }
    h1 {
      text-align: center;
      margin-bottom: 2rem;
      color: #2c3e50;
    }
    form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    input[type="file"], input[type="text"], textarea {
      padding: 0.75rem;
      border: 1px solid #ccc;
      border-radius: 8px;
      font-size: 1rem;
    }
    textarea {
      resize: vertical;
    }
    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 0.75rem;
      font-size: 1rem;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    button:hover {
      background-color: #2980b9;
    }
    .section {
      margin-top: 2rem;
    }
    .answer {
      background-color: #ecf0f1;
      padding: 1rem;
      border-radius: 8px;
      margin-top: 1rem;
      white-space: pre-wrap;
    }
    .loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      display: none;
    }
    .loader {
      border: 6px solid #f3f3f3;
      border-top: 6px solid #3498db;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }