<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SQL Query Generator</title>
</head>
<body>
    <h2>Get Data from SQL Server using OpenAI</h2>
    <form method="POST">
        <label>Table Name:</label><br>
        <input type="text" name="table" required><br><br>

        <label>Column Name:</label><br>
        <input type="text" name="column" required><br><br>

        <label>Query Description:</label><br>
        <input type="text" name="prompt" required><br><br>

        <input type="submit" value="Generate and Run SQL">
    </form>

    {% if sql_query %}
        <h3>Generated SQL Query:</h3>
        <pre>{{ sql_query }}</pre>
    {% endif %}

    {% if results %}
        <h3>Query Results:</h3>
        {% if results is string %}
            <p>{{ results }}</p>
        {% else %}
            <table border="1">
                <tr>
                    {% for col in results[0].keys() %}
                        <th>{{ col }}</th>
                    {% endfor %}
                </tr>
                {% for row in results %}
                    <tr>
                        {% for cell in row.values() %}
                            <td>{{ cell }}</td>
                        {% endfor %}
                    </tr>
                {% endfor %}
            </table>
        {% endif %}
    {% endif %}
</body>
</html>