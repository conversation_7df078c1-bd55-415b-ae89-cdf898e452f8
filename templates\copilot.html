<!DOCTYPE html>
<html>
<head>
    <title>Document Analyzer</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        input[type="file"], input[type="submit"] { margin: 10px 0; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto; }
        .section { margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>Upload a Document</h1>
    <form method="POST" enctype="multipart/form-data">
        <input type="file" name="file" required>
        <br>
        <input type="submit" value="Analyze">
    </form>

    {% if error %}
        <div class="section" style="color: red;"><strong>Error:</strong> {{ error }}</div>
    {% endif %}

    {% if text %}
        <div class="section"><strong>Language:</strong> {{ language }}</div>
        <div class="section"><strong>Word Count:</strong> {{ word_count }}</div>

        <div class="section">
            <strong>Top Keywords:</strong>
            <ul>
                {% for word, count in top_keywords %}
                    <li>{{ word }}: {{ count }}</li>
                {% endfor %}
            </ul>
        </div>

        <div class="section">
            <strong>Summary:</strong>
            <p>{{ summary }}</p>
        </div>

        <div class="section">
            <strong>Extracted Text:</strong>
            <pre>{{ text }}</pre>
        </div>
    {% endif %}
</body>
</html>