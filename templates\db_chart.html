<!DOCTYPE html>
<html>
<head>
    <title>PSIPL SQL/Oracle Chart</title> <!--Groq SQL to Chart-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body { padding-top: 2rem; background: #f8f9fa; }
        .container { max-width: 960px; }
        img.chart { border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .table { background: white; }
        pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="mb-4 text-primary">Puratech SQL/Oracle Chart Generator</h2>   <!--Groq-Powered SQL + Chart Generator-->
       <form method="post" class="card p-4 mb-4 shadow-sm">
      
    <div class="mb-3">
        <input class="form-control" name="prompt" placeholder="Describe your SQL question..." required>
    </div>
    <div class="row">
        <div class="col-md-6 mb-3">
            <input class="form-control" name="server" placeholder="SQL Server (e.g. localhost)" required>
        </div>
        <div class="col-md-6 mb-3">
            <input class="form-control" name="port" placeholder="Port (default: 1433)" value="1433" required>
        </div>
    </div>
    <div class="mb-3">
        <input class="form-control" name="database" placeholder="Database name" required>
    </div>
    <div class="row">
        <div class="col-md-6 mb-3">
            <input class="form-control" name="username" placeholder="Username" required>
        </div>
        <div class="col-md-6 mb-3">
            <input type="password" class="form-control" name="password" placeholder="Password" required>
        </div>
    </div>

    <!-- Chart type selector -->
    <div class="mb-3">
        <label for="chart_type" class="form-label">Chart Type</label>
        <select class="form-select" name="chart_type" required>
            <option value="bar" selected>Bar</option>
            <option value="line">Line</option>
            <option value="pie">Pie</option>
        </select>
    </div>

    <button class="btn btn-primary w-100">Generate Query and Chart</button>
</form>

        {% if error %}
            <div class="alert alert-danger">{{ error }}</div>
        {% endif %}

        {% if query %}
            <h5 class="mt-4">🔍 SQL Generated:</h5>
            <pre>{{ query }}</pre>
        {% endif %}

        {% if result %}
            <h5 class="mt-4">📋 Query Result:</h5>
            {{ result|safe }}
        {% endif %}

        {% if chart_path %}
            <h5 class="mt-4">📊 Chart:</h5>
            <img src="{{ url_for('static', filename='chart.png') }}" class="img-fluid chart mt-2" alt="Chart">
        {% endif %}
    </div>
</body>
</html>