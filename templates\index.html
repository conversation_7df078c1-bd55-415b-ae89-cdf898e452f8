<!DOCTYPE html>
<html>
<head>
    <title>PSIPL-AI</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
   <div class="loader-wrapper" id="loader">
    <div class="loader"></div>
     <div class="loader-text" style="text-color:red;">🔍 Analyzing your data…</div>
  </div>
  <div class="container">
    <h1>Puratech AI Assistant</h1>

    <form method="POST" enctype="multipart/form-data" onsubmit="showLoader()">
      <h2>Excel Analysis</h2>
      
    <input type="file" name="file" id="fileInput" >
    <label for="sheetSelect" style="margin-top: 10px;">Select Sheet:</label>
  <select name="sheet_name" id="sheetSelect" >
    <option value="">-- Select Sheet --</option>
  </select>
     <input type="text" name="excel_question" placeholder="Ask a question about the Excel data">
     <button type="submit" name="submit" value="Ask About Excel">Ask About Excel</button>

      {% if excel_answer %}
      <div class="section">
        <h3>Answer from Excel:</h3>
        <div class="answer">{{ excel_answer }}</div>
      </div>
      {% endif %}

      <hr>

      <h2>General AI Assistant</h2>
      <input type="text" name="general_question" placeholder="Ask a general question">
      <button type="submit" name="submit" value="Ask General">Ask General</button>

      {% if general_answer %}
      <div class="section">
        <h3>General Answer:</h3>
        <div class="answer">{{ general_answer }}</div>
      </div>
      {% endif %}
    </form>
  </div>

  <script>
    function showLoader() {
      document.getElementById("loader").style.display = "flex";
    }

 document.getElementById("fileInput").addEventListener("change", function () {
  const file = this.files[0];
  const formData = new FormData();
  formData.append("file", file);

  fetch("/sheets", {
    method: "POST",
    body: formData
  })
  .then(res => res.json())
  .then(data => {
    const select = document.getElementById("sheetSelect");
    select.innerHTML = ""; // Clear old options

    if (data.sheets && data.sheets.length > 0) {
      data.sheets.forEach(sheet => {
        const option = document.createElement("option");
        option.value = sheet;
        option.text = sheet;
        select.appendChild(option);
      });
    } else {
      const option = document.createElement("option");
      option.text = "No sheets found";
      select.appendChild(option);
    }
  });
});
  </script>
</body>
</html>