<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schema Comparison</title>
    <style>
        /* Reset some default styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f7f9fc;
            color: #333;
            line-height: 1.6;
            padding: 40px;
        }

        h1, h2, h3 {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
            margin-bottom: 20px;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
        }

        /* Flex Container for Source and Destination Sections */
        #compareForm {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            gap: 30px;
            max-width: 800px;
            margin: 0 auto;
            font-size: 1rem;
        }

        .section {
            width: 45%;
        }

        /* Section Headings */
        .section h2 {
            background-color: #3498db;
            color: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        label {
            font-size: 1.1rem;
            margin-bottom: 8px;
            display: block;
            color: #555;
        }

        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        button {
            background-color: #3498db;
            color: #fff;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #2980b9;
        }

        /* Loading Spinner */
        #loader {
            display: none;
            border: 16px solid #f3f3f3;
            border-top: 16px solid #3498db;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Results Section */
        #results {
            display: none;
            margin-top: 30px;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            font-size: 1rem;
        }

        h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        ul {
            list-style-type: none;
        }

        ul li {
            background-color: #ecf0f1;
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
        }

        pre {
            background-color: #f1f1f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>

<body>
    <h1>Compare Database Schemas</h1>
    <form id="compareForm">
        <!-- Source Section -->
        <div class="section">
            <h2>Source</h2>
            <label for="source_user">Source User:</label>
            <input type="text" id="source_user" placeholder="Enter Source User">

            <label for="source_password">Source Password:</label>
            <input type="password" id="source_password" placeholder="Enter Source Password">

            <label for="source_dsn">Source DSN:</label>
            <input type="text" id="source_dsn" placeholder="Enter Source DSN">
        </div>

        <!-- Destination Section -->
        <div class="section">
            <h2>Destination</h2>
            <label for="dest_user">Destination User:</label>
            <input type="text" id="dest_user" placeholder="Enter Destination User">

            <label for="dest_password">Destination Password:</label>
            <input type="password" id="dest_password" placeholder="Enter Destination Password">

            <label for="dest_dsn">Destination DSN:</label>
            <input type="text" id="dest_dsn" placeholder="Enter Destination DSN">
        </div>
    </form>

    <button type="button" onclick="compareSchemas()">Compare</button>
   
    <!-- Loading spinner -->
    <div id="loader"></div>

    <!-- Results -->
    <div id="results"></div>

    <script>
        function compareSchemas() {
            // Show loader
            document.getElementById('loader').style.display = 'block';
            document.getElementById('results').style.display = 'none'; // Hide results during loading

            const formData = {
                source_user: document.getElementById('source_user').value,
                source_password: document.getElementById('source_password').value,
                source_dsn: document.getElementById('source_dsn').value,
                dest_user: document.getElementById('dest_user').value,
                dest_password: document.getElementById('dest_password').value,
                dest_dsn: document.getElementById('dest_dsn').value,
            };

            fetch('/compare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            })
                .then(response => response.json())
                .then(data => {
                    let resultsHTML = `<h3>Missing Tables:</h3><ul>`;
                    data.missing_tables.forEach(table => {
                        resultsHTML += `<li>${table}</li>`;
                    });
                    resultsHTML += `</ul><h3>Changed Tables:</h3><ul>`;
                    for (const [table, columns] of Object.entries(data.changed_tables)) {
                        resultsHTML += `<li>${table}: ${columns.join(", ")}</li>`;
                    }
                    resultsHTML += `</ul><h3>Missing Procedures/Functions:</h3><ul>`;
                    data.missing_procs.forEach(proc => {
                        resultsHTML += `<li>${proc}</li>`;
                    });
                    resultsHTML += `</ul><h3>Changed Procedures/Functions:</h3><ul>`;
                    data.changed_procs.forEach(proc => {
                        resultsHTML += `<li>${proc}</li>`;
                    });
                    resultsHTML += `</ul><h3>SQL Statements:</h3><pre>${data.sql}</pre>`;

                    // Hide loader and show results
                    document.getElementById('loader').style.display = 'none';
                    document.getElementById('results').style.display = 'block';
                    document.getElementById('results').innerHTML = resultsHTML;
                })
                .catch(error => {
                    // Hide loader and show error
                    document.getElementById('loader').style.display = 'none';
                    document.getElementById('results').style.display = 'block';
                    document.getElementById('results').innerHTML = `<p style="color: red;">Error: ${error}</p>`;
                });

                
        }
    </script>
</body>

</html>